import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:card_swiper/card_swiper.dart';

class HomeSlider extends ConsumerStatefulWidget {
  const HomeSlider({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeSliderState();
}

class _HomeSliderState extends ConsumerState<HomeSlider> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(sliderVmodel.notifier).getSliders();
    });
  }

  @override
  Widget build(BuildContext context) {
    final sliderRef = ref.watch(sliderVmodel);
    return LoadableContentBuilder(
      isBusy: sliderRef.isBusy,
      isError: sliderRef.hasError,
      loadingBuilder: (context) {
        return Padding(
          padding: EdgeInsets.all(Sizer.width(8)),
          child: Skeletonizer(
              child: Bone(
            height: Sizer.height(200),
            width: Sizer.screenWidth,
          )),
        );
      },
      errorBuilder: (p0) {
        return SizedBox(
          height: Sizer.height(200),
          child: Swiper(
            autoplay: true,
            autoplayDisableOnInteraction: true,
            autoplayDelay: 5000,
            duration: 1000,
            // physics: const NeverScrollableScrollPhysics(),
            onIndexChanged: (i) {
              // currentIndex = i;
              setState(() {});
            },
            itemCount: 3,
            itemBuilder: (ctx, i) {
              return imageHelper(
                "assets/images/onboard/v$i.png",
                fit: BoxFit.cover,
              );
            },
          ),
        );
      },
      contentBuilder: (context) {
        return SizedBox(
          height: Sizer.height(200),
          child: Swiper(
            autoplay: true,
            autoplayDisableOnInteraction: true,
            autoplayDelay: 5000,
            duration: 1000,
            // physics: const NeverScrollableScrollPhysics(),
            onIndexChanged: (i) {
              // currentIndex = i;
              setState(() {});
            },
            itemCount: 3,
            itemBuilder: (ctx, i) {
              return imageHelper(
                "assets/images/onboard/v$i.png",
                fit: BoxFit.cover,
              );
            },
          ),
        );
        // return SizedBox(
        //   height: Sizer.height(200),
        //   child: Swiper(
        //     autoplay: true,
        //     autoplayDisableOnInteraction: true,
        //     autoplayDelay: 5000,
        //     duration: 1000,
        //     itemCount: sliderRef.sliders.length,
        //     itemBuilder: (ctx, i) {
        //       return MyCachedNetworkImage(
        //         imageUrl: sliderRef.sliders[i].photoUrl ?? '',
        //         fit: BoxFit.cover,
        //       );
        //     },
        //   ),
        // );
      },
    );
  }
}
