import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class RateOrderModal extends ConsumerStatefulWidget {
  const RateOrderModal({
    super.key,
    this.selectedIndex,
  });

  final int? selectedIndex;

  @override
  ConsumerState<RateOrderModal> createState() => _RateOrderModalState();
}

class _RateOrderModalState extends ConsumerState<RateOrderModal> {
  int _selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _selectedIndex = widget.selectedIndex ?? -1;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Your rating',
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                InkWell(
                  onTap: () => Navigator.pop(context),
                  child: SvgPicture.asset(AppSvgs.close),
                ),
              ],
            ),
          ),
          const YBox(16),
          Container(
            padding: EdgeInsets.all(Sizer.height(16)),
            margin: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            decoration: const BoxDecoration(
              color: AppColors.grayFA,
              // borderRadius: BorderRadius.circular(Sizer.radius(8)),
              // border: Border.all(width: 1, color: AppColors.grayF0),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(AppText.emojis.length, (i) {
                    final emoji = AppText.emojis[i];
                    return InkWell(
                      onTap: () {
                        _selectedIndex = i;
                        setState(() {});
                      },
                      child: Container(
                        padding: EdgeInsets.all(Sizer.radius(4)),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _selectedIndex == i
                              ? AppColors.black
                              : AppColors.transparent,
                        ),
                        child: Image.asset(
                          emoji["image"] ?? "",
                          height: Sizer.height(44),
                        ),
                      ),
                    );
                  }),
                ),
                const YBox(24),
                if (_selectedIndex != -1)
                  Text(
                    AppText.emojis[_selectedIndex]["desc"] ?? "",
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
          Column(
            children: [
              const YBox(24),
              const HLine(),
              const YBox(16),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(16),
                ),
                child: CustomTextField(
                  // controller: emailC,
                  // focusNode: emailF,
                  labelText: "Tell us why",
                  showLabelHeader: true,
                  borderRadius: 0,

                  onChanged: (p0) => setState(() {}),
                ),
              ),
            ],
          ),
          const YBox(30),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: CustomBtn.solid(
              onTap: () async {},
              online: true,
              text: "Rate order",
            ),
          ),
          const YBox(40),
        ],
      ),
    );
  }
}
