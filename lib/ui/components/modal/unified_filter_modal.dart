import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class UnifiedFilterModal extends ConsumerStatefulWidget {
  const UnifiedFilterModal({
    super.key,
    required this.onApply,
    this.isBusy = false,
  });

  final VoidCallback onApply;
  final bool isBusy;

  @override
  ConsumerState<UnifiedFilterModal> createState() => _UnifiedFilterModalState();
}

class _UnifiedFilterModalState extends ConsumerState<UnifiedFilterModal>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Size options
  final List<String> _sizeOptions = ['50ml', '75ml', '100ml', '120ml'];

  // Price range
  RangeValues _priceRange = const RangeValues(0, 100000);
  final double _minPrice = 0;
  final double _maxPrice = 100000;

  // Text controllers for price inputs
  final TextEditingController _fromController = TextEditingController();
  final TextEditingController _toController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize filter state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(filterVm).initializeTempFilter();
      _initializeFromCurrentFilter();
    });
  }

  void _initializeFromCurrentFilter() {
    final filter = ref.read(filterVm).tempFilter;

    // Initialize price range
    if (filter.minPrice != null || filter.maxPrice != null) {
      _priceRange = RangeValues(
        filter.minPrice ?? _minPrice,
        filter.maxPrice ?? _maxPrice,
      );
      _fromController.text = _priceRange.start.round().toString();
      _toController.text = _priceRange.end.round().toString();
    } else {
      _fromController.text = _priceRange.start.round().toString();
      _toController.text = _priceRange.end.round().toString();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fromController.dispose();
    _toController.dispose();
    super.dispose();
  }

  void _updatePriceRange(RangeValues newRange) {
    setState(() {
      _priceRange = newRange;
      _fromController.text = newRange.start.round().toString();
      _toController.text = newRange.end.round().toString();
    });

    ref.read(filterVm).updatePriceRange(
          newRange.start == _minPrice ? null : newRange.start,
          newRange.end == _maxPrice ? null : newRange.end,
        );
  }

  void _updateFromText(String value) {
    final double? newValue = double.tryParse(value);
    if (newValue != null &&
        newValue >= _minPrice &&
        newValue <= _priceRange.end) {
      setState(() {
        _priceRange = RangeValues(newValue, _priceRange.end);
      });
      ref.read(filterVm).updatePriceRange(
            newValue == _minPrice ? null : newValue,
            _priceRange.end == _maxPrice ? null : _priceRange.end,
          );
    }
  }

  void _updateToText(String value) {
    final double? newValue = double.tryParse(value);
    if (newValue != null &&
        newValue <= _maxPrice &&
        newValue >= _priceRange.start) {
      setState(() {
        _priceRange = RangeValues(_priceRange.start, newValue);
      });
      ref.read(filterVm).updatePriceRange(
            _priceRange.start == _minPrice ? null : _priceRange.start,
            newValue == _maxPrice ? null : newValue,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final filterRef = ref.watch(filterVm);
    final productRef = ref.watch(productVm);

    return Container(
      height: Sizer.screenHeight * 0.8,
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
              vertical: Sizer.height(16),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter',
                  style: AppTypography.text18.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryBlack,
                  ),
                ),
                Row(
                  children: [
                    if (!filterRef.tempFilter.isEmpty)
                      InkWell(
                        onTap: () {
                          ref
                              .read(filterVm)
                              .updateTempFilter(const FilterModel());
                          _initializeFromCurrentFilter();
                        },
                        child: Text(
                          'Clear all',
                          style: AppTypography.text14.copyWith(
                            color: AppColors.black70,
                          ),
                        ),
                      ),
                    const XBox(16),
                    InkWell(
                      onTap: () => Navigator.pop(context),
                      child: SvgPicture.asset(AppSvgs.close),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Tab bar
          Container(
            height: Sizer.height(42),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.greyF7,
              // borderRadius: BorderRadius.circular(Sizer.radius(12)),
              border: Border.all(width: 1, color: AppColors.grayF0),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(6),
              vertical: Sizer.height(4),
            ),
            child: TabBar(
              splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
              physics: const NeverScrollableScrollPhysics(),
              onTap: (int value) {},
              dividerColor: Colors.transparent,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(Sizer.radius(8)),
                color: AppColors.white,
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelColor: AppColors.primaryBlack,
              automaticIndicatorColorAdjustment: true,
              labelStyle: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
              ),
              unselectedLabelStyle: AppTypography.text14.copyWith(
                color: AppColors.black70,
                fontWeight: FontWeight.w500,
              ),
              controller: _tabController,
              tabs: const [
                Tab(text: 'Sort'),
                Tab(text: 'Category'),
                Tab(text: 'Price'),
              ],
            ),
          ),
          // Container(
          //   margin: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
          //   decoration: BoxDecoration(
          //     border: Border.all(color: AppColors.grayE6),
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          //   child: TabBar(
          //     controller: _tabController,
          //     labelColor: AppColors.white,
          //     unselectedLabelColor: AppColors.black70,
          //     indicator: BoxDecoration(
          //       color: AppColors.primaryBlack,
          //       borderRadius: BorderRadius.circular(6),
          //     ),
          //     indicatorSize: TabBarIndicatorSize.tab,
          //     dividerColor: Colors.transparent,
          //     tabs: const [
          //       Tab(text: 'Sort'),
          //       Tab(text: 'Category'),
          //       Tab(text: 'Price'),
          //     ],
          //   ),
          // ),

          const YBox(20),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSortTab(),
                _buildCategoryTab(productRef),
                _buildPriceTab(),
              ],
            ),
          ),

          // Bottom buttons
          Container(
            padding: EdgeInsets.all(Sizer.width(16)),
            child: Row(
              children: [
                Expanded(
                  child: CustomBtn.solid(
                    onTap: () {
                      ref.read(filterVm).updateTempFilter(const FilterModel());
                      _initializeFromCurrentFilter();
                    },
                    text: 'Reset',
                    isOutline: true,
                    outlineColor: AppColors.blackBD,
                    textColor: AppColors.primaryBlack,
                    onlineColor: AppColors.white,
                  ),
                ),
                const XBox(12),
                Expanded(
                  flex: 2,
                  child: CustomBtn.solid(
                    onTap: () {
                      ref.read(filterVm).applyFilter();
                      widget.onApply();
                      Navigator.pop(context);
                    },
                    text: 'Apply Filter',
                    isLoading: widget.isBusy,
                  ),
                ),
              ],
            ),
          ),
          const YBox(20),
        ],
      ),
    );
  }

  Widget _buildSortTab() {
    final filterRef = ref.watch(filterVm);

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: SortOption.options.map((option) {
          final isSelected = filterRef.tempFilter.selectedSort == option.value;

          return InkWell(
            onTap: () {
              ref.read(filterVm).updateSort(
                    isSelected ? null : option.value,
                  );
            },
            child: Container(
              margin: EdgeInsets.only(bottom: Sizer.height(12)),
              padding: EdgeInsets.symmetric(vertical: Sizer.height(16)),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.grayE6,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    option.displayName,
                    style: AppTypography.text14.copyWith(
                      color: isSelected
                          ? AppColors.primaryBlack
                          : AppColors.black70,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check,
                      color: AppColors.primaryBlack,
                      size: Sizer.height(20),
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCategoryTab(productRef) {
    final filterRef = ref.watch(filterVm);

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
      child: Wrap(
        runSpacing: Sizer.height(12),
        spacing: Sizer.width(12),
        children: List.generate(
          productRef.productCategories.length,
          (i) {
            final c = productRef.productCategories[i];
            final isSelected =
                filterRef.tempFilter.selectedCategory == c.category;

            return InkWell(
              onTap: () {
                ref.read(filterVm).updateCategory(
                      isSelected ? null : c.category,
                    );
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(12),
                  vertical: Sizer.height(8),
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                    color:
                        isSelected ? AppColors.primaryBlack : AppColors.blackBD,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  color: isSelected
                      ? AppColors.primaryBlack.withValues(alpha: 0.05)
                      : AppColors.white,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.asset(
                        productCategoryHelper(c.category ?? '').categoryimage ??
                            AppImages.extra,
                        height: Sizer.height(24),
                        width: Sizer.width(24),
                        fit: BoxFit.cover,
                      ),
                    ),
                    const XBox(8),
                    Text(
                      productCategoryHelper(c.category ?? '').categoryNmae,
                      style: AppTypography.text12.copyWith(
                        color: isSelected
                            ? AppColors.primaryBlack
                            : AppColors.black70,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPriceTab() {
    final filterRef = ref.watch(filterVm);

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Size section
          Text(
            'Size',
            style: AppTypography.text16.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primaryBlack,
            ),
          ),
          const YBox(16),
          Row(
            children: _sizeOptions.map((size) {
              final isSelected = filterRef.tempFilter.selectedSize == size;
              return Expanded(
                child: Padding(
                  padding: EdgeInsets.only(
                    right: size != _sizeOptions.last ? Sizer.width(12) : 0,
                  ),
                  child: InkWell(
                    onTap: () {
                      ref.read(filterVm).updateSize(isSelected ? null : size);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: Sizer.height(12)),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isSelected
                              ? AppColors.primaryBlack
                              : AppColors.blackBD,
                          width: isSelected ? 2 : 1,
                        ),
                        color: isSelected
                            ? AppColors.primaryBlack.withValues(alpha: 0.05)
                            : AppColors.white,
                      ),
                      child: Center(
                        child: Text(
                          size,
                          style: AppTypography.text14.copyWith(
                            color: isSelected
                                ? AppColors.primaryBlack
                                : AppColors.black70,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          const YBox(32),

          // Price range section
          Text(
            'Price range',
            style: AppTypography.text16.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primaryBlack,
            ),
          ),
          const YBox(16),

          // Range slider
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppColors.primaryBlack,
              inactiveTrackColor: AppColors.grayE6,
              thumbColor: AppColors.primaryBlack,
              overlayColor: AppColors.primaryBlack.withValues(alpha: 0.1),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
              trackHeight: 2,
            ),
            child: RangeSlider(
              values: _priceRange,
              min: _minPrice,
              max: _maxPrice,
              onChanged: _updatePriceRange,
            ),
          ),

          const YBox(16),

          // From and To input fields
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'From',
                      style: AppTypography.text14
                          .copyWith(color: AppColors.black70),
                    ),
                    const YBox(8),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(12),
                        vertical: Sizer.height(12),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blackBD),
                        color: AppColors.grayF6,
                      ),
                      child: TextField(
                        controller: _fromController,
                        keyboardType: TextInputType.number,
                        onChanged: _updateFromText,
                        style: AppTypography.text14,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                          isDense: true,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const XBox(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'To',
                      style: AppTypography.text14
                          .copyWith(color: AppColors.black70),
                    ),
                    const YBox(8),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(12),
                        vertical: Sizer.height(12),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blackBD),
                        color: AppColors.grayF6,
                      ),
                      child: TextField(
                        controller: _toController,
                        keyboardType: TextInputType.number,
                        onChanged: _updateToText,
                        style: AppTypography.text14,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                          isDense: true,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const YBox(40),
        ],
      ),
    );
  }
}
