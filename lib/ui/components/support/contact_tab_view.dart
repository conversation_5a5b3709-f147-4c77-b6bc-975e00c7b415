import 'package:bottle_king_mobile/core/core.dart';

class ContactTabView extends StatelessWidget {
  const ContactTabView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const YBox(10),
        ContactUsWidget(
          title: "Email us",
          subTitle: "Replies within 8 hrs",
          iconPath: AppSvgs.sms,
          onTap: () {},
        ),
        const YBox(16),
        ContactUsWidget(
          title: "Chat on WhatsApp",
          subTitle: "Replies ASAP",
          iconPath: AppSvgs.sms,
          onTap: () {},
        ),
        const YBox(16),
        ContactUsWidget(
          title: "Call us",
          subTitle: "Business hours everyday",
          iconPath: AppSvgs.sms,
          onTap: () {},
        ),
      ],
    );
  }
}

class ContactUsWidget extends StatelessWidget {
  const ContactUsWidget({
    super.key,
    required this.title,
    required this.subTitle,
    required this.iconPath,
    this.onTap,
  });

  final String title;
  final String subTitle;
  final String iconPath;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(12),
          horizontal: Sizer.width(16),
        ),
        decoration: BoxDecoration(
          color: AppColors.greyF7,
          borderRadius: BorderRadius.circular(Sizer.radius(10)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: .05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            SvgPicture.asset(AppSvgs.sms),
            const XBox(16),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const YBox(4),
                  Text(
                    subTitle,
                    style: AppTypography.text12.copyWith(
                      color: AppColors.black70,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
