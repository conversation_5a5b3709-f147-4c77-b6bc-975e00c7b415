import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class CreateGroupOrderScreen extends ConsumerStatefulWidget {
  const CreateGroupOrderScreen({super.key});

  @override
  ConsumerState<CreateGroupOrderScreen> createState() =>
      _CreateGroupOrderScreenState();
}

class _CreateGroupOrderScreenState
    extends ConsumerState<CreateGroupOrderScreen> {
  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return BusyOverlay(
      show: authRef.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "Create group order",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          children: [
            const YBox(50),
            const CustomTextField(
              labelText: "Group order name ",
              showLabelHeader: true,
              borderRadius: 0,
            ),
            const YBox(16),
            CustomTextField(
              labelText: "Set order timeline",
              keyboardType: KeyboardType.email,
              showLabelHeader: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(16),
            CustomTextField(
              labelText: "Choose payment method",
              keyboardType: KeyboardType.email,
              showLabelHeader: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(16),
            CustomTextField(
              labelText: "Add members",
              keyboardType: KeyboardType.email,
              showLabelHeader: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(30),
            CustomBtn.solid(
              onTap: () async {
                FocusScope.of(context).unfocus();
              },
              text: "Continue",
            ),
          ],
        ),
      ),
    );
  }
}
