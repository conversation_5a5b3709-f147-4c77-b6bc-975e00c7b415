import 'package:bottle_king_mobile/lib.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppInitService().init();

  // Initialize API service
  ApiServiceInitializer.initialize();

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(430, 932),
      builder: (context, child) {
        return MaterialApp(
          title: 'Bottle King',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.orange),
            scaffoldBackgroundColor: AppColors.white,
            fontFamily: 'GeneralSans',
            useMaterial3: true,
          ),
          navigatorKey: NavKey.appNavKey,
          onGenerateRoute: AppRouter.onGenerateRoute,
          home: const SplashScreen(),
        );
      },
    );
  }
}


// <EMAIL>