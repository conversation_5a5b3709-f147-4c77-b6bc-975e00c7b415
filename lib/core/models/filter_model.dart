class FilterModel {
  final String? selectedCategory;
  final String? selectedSort;
  final String? selectedSize;
  final double? minPrice;
  final double? maxPrice;
  final bool hasActiveFilters;

  const FilterModel({
    this.selectedCategory,
    this.selectedSort,
    this.selectedSize,
    this.minPrice,
    this.maxPrice,
    this.hasActiveFilters = false,
  });

  FilterModel copyWith({
    String? selectedCategory,
    String? selectedSort,
    String? selectedSize,
    double? minPrice,
    double? maxPrice,
    bool? hasActiveFilters,
  }) {
    return FilterModel(
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedSort: selectedSort ?? this.selectedSort,
      selectedSize: selectedSize ?? this.selectedSize,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      hasActiveFilters: hasActiveFilters ?? this.hasActiveFilters,
    );
  }

  FilterModel clearFilters() {
    return const FilterModel(
      selectedCategory: null,
      selectedSort: null,
      selectedSize: null,
      minPrice: null,
      maxPrice: null,
      hasActiveFilters: false,
    );
  }

  bool get isEmpty {
    return selectedCategory == null &&
        selectedSort == null &&
        selectedSize == null &&
        minPrice == null &&
        maxPrice == null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is FilterModel &&
        other.selectedCategory == selectedCategory &&
        other.selectedSort == selectedSort &&
        other.selectedSize == selectedSize &&
        other.minPrice == minPrice &&
        other.maxPrice == maxPrice &&
        other.hasActiveFilters == hasActiveFilters;
  }

  @override
  int get hashCode {
    return selectedCategory.hashCode ^
        selectedSort.hashCode ^
        selectedSize.hashCode ^
        minPrice.hashCode ^
        maxPrice.hashCode ^
        hasActiveFilters.hashCode;
  }

  @override
  String toString() {
    return 'FilterModel(selectedCategory: $selectedCategory, selectedSort: $selectedSort, selectedSize: $selectedSize, minPrice: $minPrice, maxPrice: $maxPrice, hasActiveFilters: $hasActiveFilters)';
  }
}

enum FilterType {
  sort,
  category,
  price,
}

class SortOption {
  final String displayName;
  final String value;
  final bool isExtraCategory;

  const SortOption({
    required this.displayName,
    required this.value,
    this.isExtraCategory = false,
  });

  static const List<SortOption> options = [
    SortOption(displayName: "Best sellers", value: "bestsellers", isExtraCategory: true),
    SortOption(displayName: "Recommended", value: "recommended", isExtraCategory: true),
    SortOption(displayName: "New arrivals", value: "newarrival", isExtraCategory: true),
    SortOption(displayName: "Discount", value: "discount", isExtraCategory: true),
    SortOption(displayName: "Offers", value: "offers"),
    SortOption(displayName: "Price low to high", value: "price_asc"),
    SortOption(displayName: "Price high to low", value: "price_desc"),
  ];
}
