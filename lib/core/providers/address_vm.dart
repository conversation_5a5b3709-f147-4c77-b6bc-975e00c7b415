import 'package:bottle_king_mobile/core/core.dart';
import 'package:collection/collection.dart';

const String predictionState = "predictionState";
const String addAddressState = "addAddressState";
const String getAddressState = "getAddressState";
const String deleteAddressState = "deleteAddressState";

class AddressVm extends BaseVm {
  //
  final String googleApiKey = AppConfig.googlePlacesApiKey;

  final List<StateProvince> _stateProvince = [];
  List<StateProvince> get stateProvince => _stateProvince;

  List<AddressPredictions> _predictions = [];
  List<AddressPredictions> get predictions => _predictions;

  List<AddressModel> _orderHistoryAddresses = [];
  List<AddressModel> get orderHistoryAddresses => _orderHistoryAddresses;

  // Get home from addresses
  List<AddressModel> _addresses = [];
  List<AddressModel> get addresses => _addresses;
  AddressModel? get homeAddress => _addresses
      .firstWhereOrNull((element) => element.type?.toLowerCase() == "home");

  DeliveryFee? _deliveryFee;
  DeliveryFee? get deliveryFee => _deliveryFee;

  // Current address is address from google places
  AddressArg? _currentAddress;
  AddressArg? get currentAddress => _currentAddress;
  setCurrentAddress(AddressArg arg) {
    _currentAddress = arg;
    _deliveryAddress = arg;
    reBuildUI();
  }

  // Delivery address is address from saved addresses
  AddressArg? _deliveryAddress;
  AddressArg? get deliveryAddress => _deliveryAddress;
  setDeliveryAddress(AddressArg arg) {
    _deliveryAddress = arg;
    reBuildUI();
  }

  OrderDeliveryType _orderDeliveryType = OrderDeliveryType.delivery;
  OrderDeliveryType get orderDeliveryType => _orderDeliveryType;
  bool get isDelivery => _orderDeliveryType == OrderDeliveryType.delivery;
  bool get isPickup => _orderDeliveryType == OrderDeliveryType.pickup;
  setOrderDeliveryType(OrderDeliveryType type) {
    _orderDeliveryType = type;
    reBuildUI();
  }

  String get address => _orderDeliveryType == OrderDeliveryType.delivery
      ? _deliveryAddress?.fullAddress ?? ''
      : AppText.pickupAddress;

  updatePredictions(List<AddressPredictions> pre) {
    _predictions = pre;
    reBuildUI();
  }

  Future<ApiResponse> getPlacePredictions(String input,
      {String countryValue = "country:ng"}) async {
    // final String googleApiKey = dotenv.env['GOOGLE_PLACES_API_KEY'] ?? "";

    final String url =
        "https://maps.googleapis.com/maps/api/place/autocomplete/json?components=$countryValue&input=$input&key=$googleApiKey";

    return await performApiCall(
      url: url,
      method: apiService.get,
      busyObjectName: predictionState,
      onSuccess: (data) {
        printty("data: $data", logName: "getPlacePredictions");
        _predictions =
            addressPredictionsFromJson(json.encode(data["predictions"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getPlaceDetails(String placeId) async {
    // final String googleApiKey = dotenv.env['GOOGLE_PLACES_API_KEY'] ?? "";

    final String url =
        "https://maps.googleapis.com/maps/api/place/details/json?place_id=$placeId&key=$googleApiKey";

    return await performApiCall(
      url: url,
      method: apiService.get,
      busyObjectName: predictionState,
      onSuccess: (data) {
        printty("data: $data", logName: "getPlaceDetails");
        return ApiResponse(
          success: true,
          data: PlacesResult.fromJson(data["result"]),
        );
      },
    );
  }

  getStateProvince() {
    List<StateProvince> allStateProvince = [];
    allStateProvince = listOfStateProvince;
    allStateProvince.sort(
      (a, b) => a.name!.toLowerCase().compareTo(b.name!.toLowerCase()),
    );

    _stateProvince.clear();
    _stateProvince.addAll(allStateProvince);
    reBuildUI();
  }

  void searchStateProvince(String query) {
    if (query.isEmpty) {
      getStateProvince();
      return;
    }

    final List<StateProvince> searchResult = _stateProvince
        .where((element) =>
            element.name!.toLowerCase().contains(query.toLowerCase()))
        .toList();

    _stateProvince.clear();
    _stateProvince.addAll(searchResult);
    reBuildUI();
  }

  Future<ApiResponse> addNewAddress({
    required AddressArg args,
  }) async {
    final body = args.toJson();
    printty("body: $body");
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/address",
      method: apiService.postWithAuth,
      body: body,
      busyObjectName: addAddressState,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> setAddressAsDefault({
    required String id,
  }) async {
    return await performApiCall(
      url: "/address/$id/set-default",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getAddresses([bool isBusy = true]) async {
    return await performApiCall(
      url: "/address",
      method: apiService.getWithAuth,
      busyObjectName: isBusy ? getAddressState : "",
      onSuccess: (data) {
        _addresses = addressFromJson(json.encode(data["data"]["addresses"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> addressOrderHistory([bool isBusy = true]) async {
    return await performApiCall(
      url: "/address/order-history",
      method: apiService.getWithAuth,
      busyObjectName: isBusy ? getAddressState : "",
      onSuccess: (data) {
        _orderHistoryAddresses = addressFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> deleteAddresses(String addressId) async {
    return await performApiCall(
      url: "/address/$addressId",
      method: apiService.deleteWithAuth,
      busyObjectName: deleteAddressState,
      onSuccess: (data) {
        addressOrderHistory(false);
        return apiResponse;
      },
    );
  }

  // Get shipping fee
  Future<ApiResponse> getShippingFee() async {
    return await performApiCall(
      url: "https://api.bottleking.ng/order/fare-rate",
      method: apiService.postWithAuth,
      body: {
        "origins": ['6.4440858, 3.5080498'],
        "destinations": ['${_deliveryAddress?.lat}, ${_deliveryAddress?.lng}'],
      },
      onSuccess: (data) {
        _deliveryFee = deliveryFeeFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}

final addressVm = ChangeNotifierProvider((ref) {
  return AddressVm();
});
